import { post } from '@zeal/api/requestBackend'

import { notReachable } from '@zeal/toolkit'
import { ImperativeError } from '@zeal/toolkit/Error'
import * as Hexadecimal from '@zeal/toolkit/Hexadecimal'
import { parse as parseJSON } from '@zeal/toolkit/JSON'
import { values } from '@zeal/toolkit/Object'
import {
    arrayOf,
    arrayOfLength,
    combine,
    failure,
    match,
    nonEmptyArray,
    nullable,
    object,
    oneOf,
    required,
    Result,
    safeArrayOf,
    shape,
    string,
    success,
} from '@zeal/toolkit/Result'
import * as Web3 from '@zeal/toolkit/Web3'

import { KnownCurrencies } from '@zeal/domains/Currency'
import { fetchCryptoCurrency2 } from '@zeal/domains/Currency/api/fetchCryptoCurrency2'
import { DAppSiteInfo } from '@zeal/domains/DApp'
import { captureError } from '@zeal/domains/Error/helpers/captureError'
import { fetchRatesForDefaultCurrency } from '@zeal/domains/FXRate/api/fetchRate'
import { applyRate2 } from '@zeal/domains/FXRate/helpers/applyRate'
import { CryptoMoney } from '@zeal/domains/Money'
import { MoneyDTO } from '@zeal/domains/Money/helpers/parse'
import {
    Network,
    NetworkMap,
    NetworkRPCMap,
    PredefinedNetwork,
} from '@zeal/domains/Network'
import { EthSendTransaction, ParsedLog } from '@zeal/domains/RPCRequest'
import { fetchTransactionsSafetyChecks } from '@zeal/domains/SafetyCheck/api/fetchTransactionsSafetyChecks'
import { DefaultCurrencyConfig } from '@zeal/domains/Storage'
import { IndexedTransactionLogDTO } from '@zeal/domains/Transactions'
import { parseLog } from '@zeal/domains/Transactions/helpers/parseLog'
import { InitialUserOperation } from '@zeal/domains/UserOperation'

import { SimulatedTransaction } from '../SimulatedTransaction'
import { SimulateTransactionResponse } from '../SimulateTransactionResponse'

const KNOWN_METHOD_HASHES: Record<Hexadecimal.Hexadecimal, string> = {
    '0x7bb37428': 'executeUserOp',
}

const UNLIMITED_APPROVAL_THRESHOLD = 10n ** 33n

export type SimulationResult =
    | { type: 'failed' }
    | { type: 'not_supported' }
    | {
          type: 'simulated'
          simulation: SimulateTransactionResponse
      }

type RequestToSimulate =
    | { type: 'rpc_request'; rpcRequest: EthSendTransaction }
    | { type: 'user_operation'; initialUserOperation: InitialUserOperation }

export type FetchSimulationByRequest2 = (_: {
    requestToSimulate: RequestToSimulate
    network: Network
    defaultCurrencyConfig: DefaultCurrencyConfig
    networkMap: NetworkMap
    dApp: DAppSiteInfo | null
    networkRPCMap: NetworkRPCMap
    signal?: AbortSignal
}) => Promise<SimulationResult>

type SimulatedTransactionRawSuccess = {
    status: 'success'
    logs: ParsedLog[]
    to: Web3.address.Address
    value: Hexadecimal.Hexadecimal | null
    input: Hexadecimal.Hexadecimal | null
}

type SimulatedTransactionRawReverted = {
    status: 'reverted'
    to: Web3.address.Address
    input: Hexadecimal.Hexadecimal | null
    value: Hexadecimal.Hexadecimal | null
    reason: string | null
}

type SimulatedTransactionRaw =
    | SimulatedTransactionRawSuccess
    | SimulatedTransactionRawReverted

const getMethodName = ({
    input,
}: {
    input: Hexadecimal.Hexadecimal
}): string => {
    // TODO @resetko-zeal some function to get slice of Hexadecimal
    const methodHash = Hexadecimal.fromBuffer(
        Hexadecimal.toBuffer(input).slice(0, 4)
    )

    return KNOWN_METHOD_HASHES[methodHash] || methodHash
}

const parseSimulatedTransactionRaw = ({
    item,
    network,
}: {
    item: unknown
    network: PredefinedNetwork
}): Result<unknown, SimulatedTransactionRaw> =>
    object(item)
        .andThen((itemObj) => object(itemObj.transaction))
        .andThen((trxObj) =>
            oneOf(trxObj, [
                shape({
                    status: match(trxObj.status, false).map(
                        () => 'reverted' as const
                    ),
                    input: Hexadecimal.nullableParse(trxObj.input),
                    value: Hexadecimal.nullableParse(trxObj.value),
                    to: Web3.address.parse(trxObj.to),
                    reason: object(trxObj.transaction_info)
                        .andThen((trxInfo) =>
                            safeArrayOf(trxInfo.stack_trace, (item) =>
                                object(item).andThen((itemObj) =>
                                    shape({
                                        op: match(itemObj.op, 'REVERT'),
                                        reason: string(itemObj.error_reason),
                                    })
                                )
                            )
                        )
                        .map((calls) => (calls[0] ? calls[0].reason : null)),
                }),

                shape({
                    status: match(trxObj.status, true).map(
                        () => 'success' as const
                    ),
                    input: Hexadecimal.nullableParse(trxObj.input),
                    value: Hexadecimal.nullableParse(trxObj.value),
                    to: Web3.address.parse(trxObj.to),
                    logs: object(trxObj.transaction_info).andThen((infoObj) =>
                        oneOf(infoObj.logs, [
                            nullable(infoObj.logs).map(() => []),
                            arrayOf(infoObj.logs, (logItem) =>
                                object(logItem)
                                    .andThen((logObj) => object(logObj.raw))
                                    .andThen((rawObj) =>
                                        shape({
                                            address: Web3.address.parse(
                                                rawObj.address
                                            ),
                                            data: Hexadecimal.nullableParse(
                                                rawObj.data
                                            ),
                                            topics: arrayOf(
                                                rawObj.topics,
                                                Hexadecimal.parse
                                            ),
                                        })
                                    )
                            )
                                .map((arr) =>
                                    arr.map(
                                        (
                                            { address, data, topics },
                                            logIndex
                                        ): IndexedTransactionLogDTO => ({
                                            address,
                                            data,
                                            logIndex,
                                            topics,
                                        })
                                    )
                                )
                                .andThen((rawLogsArr) =>
                                    combine(
                                        rawLogsArr.map((rawLog) =>
                                            parseLog(rawLog, network)
                                        )
                                    )
                                ),
                        ])
                    ),
                }),
            ])
        )

export const fetchSimulationWithSafetyChecksByRequest2: FetchSimulationByRequest2 =
    async ({
        network,
        requestToSimulate,
        defaultCurrencyConfig,
        networkMap,
        networkRPCMap,
        dApp,
        signal,
    }): Promise<SimulationResult> => {
        switch (network.type) {
            case 'predefined': {
                try {
                    if (network.isSimulationSupported) {
                        const [transactionSafetyChecks, simulation] =
                            await Promise.all([
                                fetchTransactionsSafetyChecks({
                                    dApp,
                                    network,
                                    requestToCheck: requestToSimulate,
                                    signal,
                                }),
                                fetchSimulationByRequest({
                                    network,
                                    requestToSimulate,
                                    defaultCurrencyConfig,
                                    networkMap,
                                    networkRPCMap,
                                    signal,
                                }),
                            ])

                        return {
                            type: 'simulated',
                            simulation: {
                                transaction: simulation.transaction,
                                currencies: simulation.currencies,
                                checks: [
                                    // FIXME @resetko-zeal add static check for coingecko coin validation
                                    // FIXME @resetko-zeal add check based on simulation is failed or not
                                    // FIXME @resetko-zeal for approvalcheck if smart contract is unknown, danger fail
                                    ...transactionSafetyChecks,
                                ],
                            },
                        }
                    } else {
                        return { type: 'not_supported' }
                    }
                } catch (e) {
                    captureError(e)
                    return { type: 'failed' }
                }
            }
            case 'testnet':
            case 'custom':
                return { type: 'not_supported' }
            default:
                return notReachable(network)
        }
    }

const fetchSimulationByRequest = async ({
    network,
    requestToSimulate,
    defaultCurrencyConfig,
    networkMap,
    networkRPCMap,
    signal,
}: {
    network: PredefinedNetwork
    requestToSimulate: RequestToSimulate
    defaultCurrencyConfig: DefaultCurrencyConfig
    networkMap: NetworkMap
    networkRPCMap: NetworkRPCMap
    signal?: AbortSignal
}): Promise<{
    transaction: SimulatedTransaction
    currencies: KnownCurrencies
}> => {
    switch (requestToSimulate.type) {
        case 'rpc_request':
            return fetchRPCRequestSimulation({
                network,
                defaultCurrencyConfig,
                networkMap,
                networkRPCMap,
                rpcRequest: requestToSimulate.rpcRequest,
                signal,
            })
        case 'user_operation':
            return fetchAccountAbstractionTransactionSimulation({
                network,
                initialUserOperation: requestToSimulate.initialUserOperation,
                defaultCurrencyConfig,
                networkMap,
                networkRPCMap,
                signal,
            })
        /* istanbul ignore next */
        default:
            return notReachable(requestToSimulate)
    }
}

const fetchRPCRequestSimulation = async ({
    network,
    rpcRequest,
    signal,
    defaultCurrencyConfig,
    networkMap,
    networkRPCMap,
}: {
    network: PredefinedNetwork
    rpcRequest: EthSendTransaction
    defaultCurrencyConfig: DefaultCurrencyConfig
    networkMap: NetworkMap
    networkRPCMap: NetworkRPCMap
    signal?: AbortSignal
}): Promise<{
    transaction: SimulatedTransaction
    currencies: KnownCurrencies
}> => {
    const trx = rpcRequest.params[0]

    const tenderlyResponse = string(
        await post(
            '/proxy/simulator/simulate',
            {
                body: {
                    network_id: parseInt(network.hexChainId, 16),
                    from: trx.from,
                    to: trx.to,
                    input: trx.data,
                    save: true,
                    simulation_type: 'quick',
                    value: trx.value,
                },
            },
            signal
        )
    )
        .andThen(parseJSON)
        .getSuccessResultOrThrow(
            'Failed to parse batch simulation response JSONs'
        )

    const rawTransaction = parseSimulatedTransactionRaw({
        item: tenderlyResponse,
        network,
    }).getSuccessResultOrThrow('Failed to parse raw transaction for EoA sim')

    switch (rawTransaction.status) {
        case 'success': {
            const classifiedTransaction = parseClassifiedTransaction({
                network,
                transaction: rawTransaction,
                sender: trx.from as Web3.address.Address,
            }).getSuccessResultOrThrow(
                'Failed to classify EoA transaction after simulation'
            )

            const simulatedTransctionResponse =
                await enrichClassifiedTransaction({
                    classifiedTransaction,
                    defaultCurrencyConfig,
                    network,
                    networkMap,
                    networkRPCMap,
                    signal,
                })

            return {
                currencies: simulatedTransctionResponse.knownCurrencies,
                transaction: simulatedTransctionResponse.simulatedTransaction,
            }
        }

        case 'reverted':
            return {
                currencies: {},
                transaction: {
                    type: 'FailedTransaction',
                    method: rawTransaction.input
                        ? getMethodName({ input: rawTransaction.input })
                        : '', // TODO @resetko-zeal we should not have name if it's native transfer, although it can also fail
                },
            }

        default:
            return notReachable(rawTransaction)
    }
}

const enrichClassifiedTransaction = async ({
    classifiedTransaction,
    network,
    defaultCurrencyConfig,
    networkMap,
    networkRPCMap,
    signal,
}: {
    network: PredefinedNetwork
    classifiedTransaction: ClassifiedTransaction
    defaultCurrencyConfig: DefaultCurrencyConfig
    networkMap: NetworkMap
    networkRPCMap: NetworkRPCMap
    signal?: AbortSignal
}): Promise<{
    simulatedTransaction: SimulatedTransaction
    knownCurrencies: KnownCurrencies
}> => {
    switch (classifiedTransaction.type) {
        case 'send_native':
        case 'send_erc20': {
            const knownCryptoCurrencies = await fetchCryptoCurrency2({
                currencies: [classifiedTransaction.amount.currencyId],
                networkRPCMap,
            })

            // TODO @resetko-zeal find a way to fetch both cryptocurrencies and rates at the same time in parallel
            const rates = await fetchRatesForDefaultCurrency({
                cryptoCurrencies: values(knownCryptoCurrencies),
                networkMap,
                networkRPCMap,
                defaultCurrencyConfig,
                signal,
            })

            const cryptoCurrency =
                knownCryptoCurrencies[
                    classifiedTransaction.amount.currencyId
                ] || null

            if (!cryptoCurrency) {
                throw new ImperativeError('Failed to fetch cryptocurrency for ')
            }

            const rate = rates[classifiedTransaction.amount.currencyId]

            return {
                simulatedTransaction: {
                    type: 'P2PTransaction',
                    toAddress: classifiedTransaction.to,
                    token: {
                        amount: classifiedTransaction.amount,
                        direction: 'Send',
                        priceInDefaultCurrency: rate
                            ? applyRate2({
                                  baseAmount: {
                                      amount: classifiedTransaction.amount
                                          .amount,
                                      currency: cryptoCurrency,
                                  },
                                  rate,
                              })
                            : null,
                    },
                },
                knownCurrencies: {
                    [defaultCurrencyConfig.defaultCurrency.id]:
                        defaultCurrencyConfig.defaultCurrency,
                    [cryptoCurrency.id]: cryptoCurrency,
                },
            }
        }

        case 'approval': {
            const knownCryptoCurrencies = await fetchCryptoCurrency2({
                currencies: [classifiedTransaction.amount.currencyId],
                networkRPCMap,
            })

            const cryptoCurrency =
                knownCryptoCurrencies[
                    classifiedTransaction.amount.currencyId
                ] || null

            if (!cryptoCurrency) {
                throw new ImperativeError('Failed to fetch cryptocurrency for ')
            }

            const cryptoMoney: CryptoMoney = {
                amount: classifiedTransaction.amount.amount,
                currency: cryptoCurrency,
            }

            return {
                simulatedTransaction: {
                    type: 'ApprovalTransaction',
                    // FIXME @resetko-zeal smart contract lookup
                    approveTo: {
                        address: classifiedTransaction.spender,
                        logo: null,
                        name: null,
                        website: null,
                        networkHexId: network.hexChainId,
                    },
                    amount:
                        classifiedTransaction.amount.amount >
                        UNLIMITED_APPROVAL_THRESHOLD
                            ? { type: 'Unlimited', amount: cryptoMoney } // TODO @resetko-zeal feels that this is presentation layer info
                            : { type: 'Limited', amount: cryptoMoney },
                },
                knownCurrencies: knownCryptoCurrencies,
            }
        }

        case 'unknown': {
            const knownCryptoCurrencies = await fetchCryptoCurrency2({
                currencies: [
                    ...classifiedTransaction.receive.map(
                        (amount) => amount.currencyId
                    ),
                    ...classifiedTransaction.send.map(
                        (amount) => amount.currencyId
                    ),
                ],
                networkRPCMap,
            })

            // TODO @resetko-zeal find a way to fetch both cryptocurrencies and rates at the same time in parallel
            const rates = await fetchRatesForDefaultCurrency({
                cryptoCurrencies: values(knownCryptoCurrencies),
                networkMap,
                networkRPCMap,
                defaultCurrencyConfig,
                signal,
            })

            return {
                simulatedTransaction: {
                    type: 'UnknownTransaction',
                    method: classifiedTransaction.method || '',
                    nfts: [], // TODO @resetko-zeal classify NFT logs
                    tokens: [
                        ...classifiedTransaction.send.map((sendDto) => {
                            const currency =
                                knownCryptoCurrencies[sendDto.currencyId] ||
                                null

                            if (!currency) {
                                throw new ImperativeError(
                                    'Failed to find CryptoCurrency for send token'
                                )
                            }

                            const rate = rates[currency.id] || null

                            return {
                                direction: 'Send' as const,
                                amount: sendDto,
                                priceInDefaultCurrency: rate
                                    ? applyRate2({
                                          baseAmount: {
                                              amount: sendDto.amount,
                                              currency,
                                          },
                                          rate,
                                      })
                                    : null,
                            }
                        }),
                        ...classifiedTransaction.receive.map((receiveDto) => {
                            const currency =
                                knownCryptoCurrencies[receiveDto.currencyId] ||
                                null

                            if (!currency) {
                                throw new ImperativeError(
                                    'Failed to find CryptoCurrency for receive token'
                                )
                            }

                            const rate = rates[currency.id] || null

                            return {
                                direction: 'Receive' as const,
                                amount: receiveDto,
                                priceInDefaultCurrency: rate
                                    ? applyRate2({
                                          baseAmount: {
                                              amount: receiveDto.amount,
                                              currency,
                                          },
                                          rate,
                                      })
                                    : null,
                            }
                        }),
                    ],
                },
                knownCurrencies: {
                    ...knownCryptoCurrencies,
                    [defaultCurrencyConfig.defaultCurrency.id]:
                        defaultCurrencyConfig.defaultCurrency,
                },
            }
        }

        default:
            return notReachable(classifiedTransaction)
    }
}

const fetchAccountAbstractionTransactionSimulation = async ({
    initialUserOperation,
    network,
    defaultCurrencyConfig,
    networkMap,
    networkRPCMap,
    signal,
}: {
    initialUserOperation: InitialUserOperation
    network: PredefinedNetwork
    defaultCurrencyConfig: DefaultCurrencyConfig
    networkMap: NetworkMap
    networkRPCMap: NetworkRPCMap
    signal?: AbortSignal
}): Promise<{
    transaction: SimulatedTransaction
    currencies: KnownCurrencies
}> => {
    const { initCode, callData, entrypoint, sender } = initialUserOperation

    const deployTx: EthSendTransaction['params'][0] | null = initCode
        ? {
              from: entrypoint,
              to: initCode.slice(0, 42),
              data: `0x${initCode.slice(42)}`,
          }
        : null

    const userOpBundleTx: EthSendTransaction['params'][0] = {
        data: callData,
        from: entrypoint,
        to: sender,
    }

    const transactions = deployTx
        ? [deployTx, userOpBundleTx]
        : [userOpBundleTx]

    const tenderlyResponse = string(
        await post(
            '/proxy/simulator/simulate-bundle',
            {
                body: {
                    simulations: transactions.map(
                        (trx) =>
                            ({
                                network_id: parseInt(network.hexChainId, 16),
                                from: trx.from,
                                to: trx.to,
                                input: trx.data,
                                save: true,
                                simulation_type: 'quick',
                                value: trx.value,
                            }) as const
                    ),
                },
            },
            signal
        )
    )
        .andThen(parseJSON)
        .getSuccessResultOrThrow(
            'Failed to parse batch simulation response JSONs'
        )

    const rawTransactions = object(tenderlyResponse)
        .andThen((obj) =>
            arrayOf(obj.simulation_results, (item) =>
                parseSimulatedTransactionRaw({ item, network })
            ).andThen(nonEmptyArray)
        )
        .getSuccessResultOrThrow(
            'Failed to parse raw simulated batch transactions'
        )

    // TODO @resetko-zeal use grouping instead if double filtering
    const failedTransaction = rawTransactions.find(
        (trx): trx is SimulatedTransactionRawReverted => {
            switch (trx.status) {
                case 'success':
                    return false
                case 'reverted':
                    return true
                default:
                    return notReachable(trx)
            }
        }
    )

    if (failedTransaction) {
        return {
            currencies: {},
            transaction: {
                type: 'FailedTransaction',
                method: failedTransaction.input
                    ? getMethodName({ input: failedTransaction.input })
                    : '', // TODO @resetko-zeal we should not have name if it's native transfer, although it can also fail
            },
        }
    }

    // TODO @resetko-zeal use grouping instead if double filtering
    const successTransactions = rawTransactions.filter(
        (trx): trx is SimulatedTransactionRawSuccess => {
            switch (trx.status) {
                case 'success':
                    return true
                case 'reverted':
                    return false
                default:
                    return notReachable(trx)
            }
        }
    )

    const mainTransaction =
        successTransactions[successTransactions.length - 1] || null

    if (!mainTransaction) {
        throw new ImperativeError('Failed to get main transaction in bundle')
    }

    const classifiedTransaction = parseClassifiedTransaction({
        network,
        sender: sender as Web3.address.Address,
        transaction: mainTransaction,
    }).getSuccessResultOrThrow(
        'Failed to classify transaction for user op main transaction'
    )

    const simulatedTransctionResponse = await enrichClassifiedTransaction({
        classifiedTransaction,
        defaultCurrencyConfig,
        network,
        networkMap,
        networkRPCMap,
        signal,
    })

    return {
        currencies: simulatedTransctionResponse.knownCurrencies,
        transaction: simulatedTransctionResponse.simulatedTransaction,
    }
}

type SendERC20 = {
    type: 'send_erc20'
    to: Web3.address.Address
    amount: MoneyDTO
}

type SendNative = {
    type: 'send_native'
    to: Web3.address.Address
    amount: MoneyDTO
}

type Approval = {
    type: 'approval'
    amount: MoneyDTO
    spender: Web3.address.Address
}

type Unknown = {
    type: 'unknown'
    method: string | null
    receive: MoneyDTO[]
    send: MoneyDTO[]
}

type ClassifiedTransaction = SendERC20 | SendNative | Approval | Unknown

const parseSendNative = ({
    network,
    transaction,
}: {
    transaction: SimulatedTransactionRawSuccess
    network: PredefinedNetwork
}): Result<unknown, SendNative> =>
    oneOf(transaction, [
        shape({
            emptyInput: nullable(transaction.input),
            value: required(transaction.value).map(Hexadecimal.toBigInt),
        }),

        shape({
            value: arrayOfLength(1, transaction.logs)
                .map((logs) => logs[0])
                .andThen((log) => {
                    switch (log.type) {
                        case 'safe_module_transaction':
                            return success(log)
                        case 'account_deployed':
                        case 'added_owner':
                        case 'approval':
                        case 'disable_module':
                        case 'enable_module':
                        case 'erc20_transfer':
                        case 'safe_received':
                        case 'set_allowance':
                        case 'threshold_updated':
                        case 'unknown':
                        case 'user_operation_event':
                            return failure({
                                type: 'not_safe_module_transaction',
                                log,
                            })

                        default:
                            return notReachable(log)
                    }
                })
                .map((log) => log.value),
        }),
    ]).map(({ value }) => ({
        type: 'send_native',
        amount: { amount: value, currencyId: network.nativeCurrency.id },
        to: transaction.to,
    }))

const parseSendERC20 = ({
    transaction,
    sender,
}: {
    sender: Web3.address.Address
    transaction: SimulatedTransactionRawSuccess
}): Result<unknown, SendERC20> =>
    arrayOfLength(1, transaction.logs)
        .map((logs) => logs[0])
        .andThen((log) => {
            switch (log.type) {
                case 'erc20_transfer':
                    return success(log)
                case 'safe_module_transaction':
                case 'account_deployed':
                case 'added_owner':
                case 'approval':
                case 'disable_module':
                case 'enable_module':
                case 'safe_received':
                case 'set_allowance':
                case 'threshold_updated':
                case 'unknown':
                case 'user_operation_event':
                    return failure({ type: 'not_erc20_transfer_log', log })

                default:
                    return notReachable(log)
            }
        })
        .andThen((log) =>
            log.from === sender
                ? success(log)
                : failure({ type: 'wrong_erc20_transfer_sender', log })
        )
        .map((log) => ({
            type: 'send_erc20',
            amount: { amount: log.amount, currencyId: log.currencyId },
            to: log.to,
        }))

const parseApproval = ({
    transaction,
    sender,
}: {
    sender: Web3.address.Address
    transaction: SimulatedTransactionRawSuccess
}): Result<unknown, Approval> =>
    arrayOfLength(1, transaction.logs)
        .map((logs) => logs[0])
        .andThen((log) => {
            switch (log.type) {
                case 'approval':
                    return success(log)
                case 'erc20_transfer':
                case 'safe_module_transaction':
                case 'account_deployed':
                case 'added_owner':
                case 'disable_module':
                case 'enable_module':
                case 'safe_received':
                case 'set_allowance':
                case 'threshold_updated':
                case 'unknown':
                case 'user_operation_event':
                    return failure({ type: 'not_approval_log', log })

                default:
                    return notReachable(log)
            }
        })
        .andThen((log) =>
            log.owner === sender
                ? success(log)
                : failure({ type: 'wrong_approval_owner', log })
        )
        .map((log) => ({
            type: 'approval',
            amount: { amount: log.amount, currencyId: log.currencyId },
            spender: log.spender,
        }))

const parseUnknown = ({
    transaction,
    network,
    sender,
}: {
    sender: Web3.address.Address
    network: PredefinedNetwork
    transaction: SimulatedTransactionRawSuccess
}): Result<unknown, Unknown> => {
    const { erc20Receive, erc20Send, nativeSafeReceive, nativeSafeSend } =
        transaction.logs.reduce(
            (acc, log) => {
                switch (log.type) {
                    case 'erc20_transfer':
                        if (log.from === sender) {
                            acc.erc20Send.push({
                                amount: log.amount,
                                currencyId: log.currencyId,
                            })
                        }
                        if (log.to === sender) {
                            acc.erc20Receive.push({
                                amount: log.amount,
                                currencyId: log.currencyId,
                            })
                        }
                        return acc

                    case 'safe_module_transaction':
                        if (log.value > 0n && log.from === sender) {
                            acc.nativeSafeSend.push({
                                amount: log.value,
                                currencyId: network.nativeCurrency.id,
                            })
                        }
                        return acc

                    case 'safe_received':
                        if (log.value > 0n && log.to === sender) {
                            acc.erc20Receive.push({
                                amount: log.value,
                                currencyId: network.nativeCurrency.id,
                            })
                        }
                        return acc

                    case 'added_owner':
                    case 'approval':
                    case 'account_deployed':
                    case 'threshold_updated':
                    case 'set_allowance':
                    case 'enable_module':
                    case 'disable_module':
                    case 'user_operation_event':
                    case 'unknown':
                        return acc
                    default:
                        return notReachable(log)
                }
            },
            {
                erc20Receive: [],
                erc20Send: [],
                nativeSafeSend: [],
                nativeSafeReceive: [],
            } as {
                erc20Receive: MoneyDTO[]
                erc20Send: MoneyDTO[]
                nativeSafeReceive: MoneyDTO[]
                nativeSafeSend: MoneyDTO[]
            }
        )

    const trxValue = transaction.value
        ? Hexadecimal.toBigInt(transaction.value)
        : null

    const nativeEoASend: MoneyDTO[] =
        trxValue && trxValue > 0n
            ? [{ amount: trxValue, currencyId: network.nativeCurrency.id }]
            : []

    return success({
        type: 'unknown',
        method: transaction.input
            ? getMethodName({ input: transaction.input })
            : null,
        send: [...nativeEoASend, ...nativeSafeSend, ...erc20Send],
        receive: [...nativeSafeReceive, ...erc20Receive],
    })
}

const parseClassifiedTransaction = ({
    network,
    transaction,
    sender,
}: {
    transaction: SimulatedTransactionRawSuccess
    sender: Web3.address.Address
    network: PredefinedNetwork
}): Result<unknown, ClassifiedTransaction> =>
    oneOf(transaction, [
        parseSendNative({ network, transaction }),
        parseSendERC20({ transaction, sender }),
        parseApproval({ transaction, sender }),
        parseUnknown({ network, transaction, sender }),
    ])
