import { str as hash } from 'crc-32'
import memoize from 'lodash.memoize'

import { isProduction } from '@zeal/toolkit/Environment'

export type Feature = 'live_activities' | 'landing_screen_2' | 'card_onboarding'

const isEnabled = (installationId: string, feature: Feature): boolean => {
    if (!isProduction()) {
        return true
    }
    const hashed = hash(`${installationId}-${feature}`)
    return hashed % 2 === 0
}

export const isZealIndexerPortfolioEnabled = () =>
    process.env.NODE_ENV !== 'test'

export const isLandingScreen2Enabled = (installationId: string) =>
    isFeatureEnabled(installationId, 'landing_screen_2')

export const isCardOnboardingEnabled = (installationId: string) =>
    isFeatureEnabled(installationId, 'card_onboarding')

export const isFeatureEnabled = memoize(
    isEnabled,
    (installationId, feature) => `${installationId}-${feature}`
)
